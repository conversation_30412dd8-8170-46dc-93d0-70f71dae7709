from typing import Optionalfrom typing import Optional

## CRM Service & Task Architecture Design

### 1. Схема базы данных

#### 1.1 Core Entities

| Entity                        | Описание                                    | Поля                                                                                                                                                                                                                    | Связи                                                                  |
|-------------------------------|---------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------|
| **Client**                    | Клиент (компания)                           | `id PK`, `name`, `ein`, `active_since`, `status`                                                                                                                                                                        | 1─\* ClientSubscription; 1─\* ClientService; 1─\* TaxReport; 1─\* Task |
| **Subscription**              | Определение подписки (пакета услуг)         | `id PK`, `title`, `price`, `price_type`, `description`                                                                                                                                                                  | 1─\* SubscriptionService; 1─\* ClientSubscription                      |
| **Service**                   | Определение отдельной услуги                | `id PK`, `title`, `price`, `price_type`, `description`                                                                                                                                                                  | 1─\* SubscriptionService; 1─\* ClientService                           |
| **SubscriptionService**       | Связь подписки с услугами                   | `id PK`, `subscription_id FK<Subscription>`, `service_id FK<Service>`                                                                                                                                                   | \*─1 Subscription; \*─1 Service                                        |
| **ClientSubscription**        | Применение подписки к клиенту               | `id PK`, `client_id FK<Client>`, `subscription_id FK<Subscription>`, `start_date`, `stop_date?`, `sum_price_services`                                                                                                   | \*─1 Client; \*─1 Subscription; 1─\* ClientSubscriptionService         |
| **ClientSubscriptionService** | Кастомизированные услуги в подписке клиента | `id PK`, `client_subscription_id FK<ClientSubscription>`, `service_id FK<Service>`, `is_active`, `custom_price?`, `note`                                                                                                | \*─1 ClientSubscription; \*─1 Service                                  |
| **ClientService**             | Применение отдельной услуги к клиенту       | `id PK`, `client_id FK<Client>`, `service_id FK<Service>`, `discount`, `start_date`, `end_date?`, `paid_date?`, `completed_date?`, `note`                                                                               | \*─1 Client; \*─1 Service; 1─\* Task                                   |
| **Task**                      | Рабочая задача                              | `id PK`, `client_service_id FK<ClientService>?`, `tax_report_id FK<TaxReport>?`, `title`, `description`, `due_date`, `assignee_manager_id`, `status {OPEN, IN_PROGRESS, COMPLETED}`                                     | \*─1 ClientService or \*─1 TaxReport                                   |
| **TaxReport**                 | Налоговая отчётность                        | `id PK`, `client_id FK<Client>`, `name`, `fiscal_year`, `assignee_manager_id`, `status {DOCUMENT_COLLECTION, PREPARATION, REVIEW, AWAITING_SIGNATURE, FILED, COMPLETED}`, `progress_steps JSON`, `file_id`, `file_name` | *─1 Client; 1─* Task                                                   |
| **NotificationRule**          | Правило оповещения                          | `id PK`, `object_type {Task, TaxReport}`, `object_id`, `offset_days`, `frequency {once, daily}`, `repeat_until_due`, `channel {email, slack, in_app}`                                                                   | 1─\* Notification                                                      |
| **Notification**              | Сформированное оповещение                   | `id PK`, `notification_rule_id FK<NotificationRule>`, `target_user_id`, `object_id`, `sent_at`, `status {SENT, FAILED}`                                                                                                 | \*─1 NotificationRule                                                  |

### 2. Структура данных моделей (Pydantic)

#### 2.1 ClientSubscriptionModel
```python
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class SubscriptionModel(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    price: float
    price_type: str  # "monthly", "quarterly", "annual", "one-time"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class ServiceModel(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    price: float

class ClientSubscriptionServiceModel(BaseModel):
    service_id: int
    service: ServiceModel
    is_active: bool = True
    custom_price: Optional[float] = None
    note: Optional[str] = None

class ClientSubscriptionModel(BaseModel):
    subscription_id: int
    subscription: SubscriptionModel
    start_date: datetime
    stop_date: Optional[datetime] = None
    services: List[ClientSubscriptionServiceModel]
    total_price: float
    status: str  # "active", "paused", "cancelled", "expired"
    discount_amount: Optional[float] = None
    discount_percent: Optional[float] = None
    cancellation_at: Optional[datetime] = None
    cancellation_reason: Optional[str] = None
    note: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

#### 2.2 ClientServicesModel
```python
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ServiceID(BaseModel):
    id: str


class ClientServiceModel(BaseModel):
    service_id: Optional[str] = None
    service: Optional[ServiceID] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    paid_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None
```

### 3. Механизм взаимодействия и воркфлоу

1. **Применение подписки и услуг**:

   * При создании записи в `ClientSubscription`:
     * Создается связь клиента с подпиской
     * Устанавливается `start_date`, опционально `stop_date`
     * По умолчанию копируются все услуги из `SubscriptionService` в `ClientSubscriptionService`
     * Клиент может **кастомизировать** подписку:
       - Отключить определенные услуги (`is_active = false`)
       - Установить индивидуальную цену для услуги (`custom_price`)
       - Добавить заметки к конкретной услуге (`note`)
     * Рассчитывается `total_price` на основе активных услуг с учетом кастомных цен и скидок

   * При создании записи в `ClientService`:
     * Создается связь клиента с отдельной услугой (не входящей в подписку)
     * Устанавливается скидка (`discount`), даты начала и окончания
     * Отслеживается статус оплаты (`paid_date`) и выполнения (`completed_date`)
     * Добавляются заметки (`note`) при необходимости
     * Создаются связанные задачи (`Task`) для выполнения услуги

   * **Кастомизация подписки через `ClientSubscriptionService`**:
     * При подключении подписки автоматически создаются записи для всех услуг из `SubscriptionService`
     * Клиент может отключить ненужные услуги, установив `is_active = false`
     * Возможность установить индивидуальную цену (`custom_price`) вместо стандартной цены услуги
     * Система пересчитывает `total_price` в `ClientSubscription` при изменении активных услуг
     * Задачи (`Task`) создаются только для активных услуг (`is_active = true`)

2. **Управление задачами**:

   * `Task` имеет статус и дедлайн. Менеджер включает в себя логику изменения статуса в UI.
   * Новые задачи не генерируются автоматически планировщиком: генерация происходит только при добавлении услуги или вручную.

3. **Tax Reporting**:

   * При создании `TaxReport` устанавливается статус `DOCUMENT_COLLECTION` и создаётся чек-лист в `progress_steps`.
   * Шаги прогресса (сбор данных, подготовка, проверка, подписание, подача) отражаются в `status` и `progress_steps`.
   * После загрузки файла (`file_id`, `file_name`) статус меняется на `COMPLETED` и связанные задачи закрываются.

4. **Оповещения и правила**:

   * `NotificationRule` определяет, за сколько дней до дедлайна и с какой частотой отправлять уведомления.
   * У правил оповещения можно настроить канал(ы) оповещений - Email / SMS / Slack / Telegram etc
   * Если сработало оповещение - помечаем и сможем показывать таких клиентов и их тасок как "подгорающие"
   * Алерты можно завершить вручную или если пометить соответствующую таску в COMPLETED
   * **Scheduler (Taskiq + Redis)**: запускается ежечасно, выбирает все активные `NotificationRule` и проверяет задачи (`Task`) и отчёты (`TaxReport`) на условия оповещений.
   * При совпадении создаётся запись `Notification` и отправляется через указанный канал.
   * Если `repeat_until_due = true`, уведомления повторяются каждый период до дедлайна.

### 4. План реализации

#### Фаза 1: Проектирование и настройка

* [Done] Создать SQLAlchemy-модели для всех сущностей и связей.
* [Done] Настроить миграции (Alembic) для схемы БД.
* Разработать репозитории/DAO слои для CRUD операций.

#### Фаза 2: Бизнес-логика и workflow

* Реализовать сервис-слой на FastAPI:

  * Методы для создания `Subscription`, `ClientSubscription`, `ClientService`, `Task`, `TaxReport`, `NotificationRule`.
  * Обработчики для управления подписками и отдельными услугами.
  * Логика расчета `sum_price_services` для подписок.
* Внедрить логику изменения статусов и управления прогрессом налоговых отчётов.

#### Фаза 3: Система уведомлений

* Интегрировать Taskiq + Redis:

  * Написать планировщик, который вовремя запускает отправку уведомлений.
  * Имплементировать механизмы проверки `NotificationRule` и создания `Notification`.
* Подключить адаптеры отправки (SMTP для email, Slack API, in-app сокеты).

#### Фаза 4: Хранение файлов

* Реализовать хранилище:

  * Сделать БД таблицу `files` для метаданных.
  * Генерация `file_id` и сохранение файла через RemoteManager.
  * Сервис для отдачи файлов по `file_id`.
