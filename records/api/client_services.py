import datetime
import os
from typing import Optional, List

from fastapi import APIRouter, HTTPException
from fastapi import Response
from pydantic import BaseModel, field_validator

from records.db import api as db_api
from records.db import base
from records.db import models
from records.utils import json_utils


class ServiceID(BaseModel):
    id: str


class ClientServiceCreate(BaseModel):
    service_id: Optional[str] = None
    service: Optional[ServiceID] = None
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)
    

class ClientServiceUpdate(BaseModel):
    service_id: Optional[str] = None
    service: Optional[ServiceID] = None
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'services'))

    router.add_api_route("", list_services, methods=['GET'], name='List client services')
    router.add_api_route("", create_client_service, methods=['POST'], name='Create client service')
    router.add_api_route("/{client_service_id}", get_client_service, methods=['GET'], name='Get client service')
    router.add_api_route("/{client_service_id}", update_client_service, methods=['PUT'], name='Update client service')
    router.add_api_route("/{client_service_id}", delete_client_service, methods=['DELETE'], name='Delete client service')

    return router


async def list_services(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    services = await db_api.list_client_services(client_id=client_id)
    return {'items': services}


async def get_client_service(client_id: str, client_service_id: int):
    client_service_db = await db_api.get_client_service_by_id(client_service_id)
    return client_service_db.to_dict()


async def create_client_service(client_id: str, service: ClientServiceCreate, assign_to: Optional[str] = None):
    # Check if service exists
    service_id = service.service_id or service.service.id
    service_db = await db_api.get_service_by_id(service_id)
    if not service_db:
        raise HTTPException(400, f'Service id={service_id} does not exist')

    client_service_dict = service.model_dump(exclude_unset=True)
    # Set start date to today if not set
    if not client_service_dict.get('start_date'):
        client_service_dict['start_date'] = datetime.datetime.now(datetime.timezone.utc)

    # Check discount
    if client_service_dict.get('discount_percent') and client_service_dict.get('discount_amount'):
        raise HTTPException(400, 'Cannot specify both discount_percent and discount_amount')

    # Calculate total with discount if applicable
    if client_service_dict.get('discount_percent'):
        client_service_dict['total'] = f"{service_db.price * (1 - client_service_dict['discount_percent'] / 100):.2f}"
    elif client_service_dict.get('discount_amount'):
        # Discount amount cannot be more than service price
        if float(client_service_dict['discount_amount']) > service_db.price:
            raise HTTPException(400, f'Discount amount cannot be more than service price {service_db.price}')
        client_service_dict['total'] = f"{service_db.price - float(client_service_dict['discount_amount']):.2f}"
    else:
        client_service_dict['total'] = f"{service_db.price:.2f}"

    client_service_dict['client_id'] = client_id
    async with base.session_context() as session:
        db_client_service = await db_api.create_client_service(client_service_dict)
        # Create components
        components_db = []
        if service.components:
            for component in service.components:
                component_dict = component.model_dump()
                component_dict['client_service_id'] = db_client_service.id
                component_db = await db_api.create_client_service_component(component_dict)
                components_db.append(component_db)
        
        # Create necessary tasks and assign to manager
        await _generate_service_tasks(client_id, db_client_service, service_db)

    return db_client_service.to_dict()


async def update_client_service(client_id: str, client_service_id: int, service: ClientServiceUpdate):
    client_service_db = await db_api.get_client_service_by_id(client_service_id)
    client_service_dict = service.model_dump()
    service_id = service.service_id or service.service.id

    client_service_dict['service_id'] = service_id
    client_service_dict.pop('service', None)

    updated_client = await db_api.update_client_service(client_service_db, client_service_dict)
    return updated_client.to_dict()


async def delete_client_service(client_id: str, client_service_id: int, delete_tasks: bool = False):
    client_service_db = await db_api.get_client_service_by_id(client_service_id)
    if delete_tasks:
        await db_api.delete_client_tasks(client_id=client_id, client_service_id=client_service_id)
    await db_api.delete_client_service(client_service_id)
    return Response(status_code=204)


async def _generate_service_tasks(client_id: str, client_service, service):
    """Generate necessary tasks for a client service based on price type."""
    # Get client information to get manager_id
    client = await db_api.get_client_by_id(client_id)
    if not client:
        raise HTTPException(400, f'Client id={client_id} does not exist')

    # Skip task creation if client has no manager assigned
    if not client.manager_id:
        return  # No manager assigned, skip task creation

    # Calculate due date based on service price type
    start_date = client_service.start_date
    due_date = _calculate_due_date(start_date, service.price_type)

    # Generate task name and description based on service
    task_name = _generate_task_name(service)
    task_description = _generate_task_description(service)

    # Create the task
    task_dict = {
        'client_id': client_id,
        'name': task_name,
        'description': task_description,
        'due_date': due_date,
        'status': models.TaskStatus.OPEN,
        'client_service_id': client_service.id,
        'manager_id': client.manager_id,
        'date': datetime.datetime.now(datetime.timezone.utc)
    }

    await db_api.create_client_task(task_dict)


def _calculate_due_date(start_date: datetime.datetime, price_type: str) -> datetime.datetime:
    """Calculate due date based on service price type."""
    if price_type == models.ServicePriceType.MONTHLY:
        return start_date + models.datetime.timedelta(days=30)
    elif price_type == models.ServicePriceType.QUARTERLY:
        return start_date + datetime.timedelta(days=90)
    elif price_type == models.ServicePriceType.ANNUAL:
        return start_date + datetime.timedelta(days=365)
    elif price_type == models.ServicePriceType.ONE_TIME:
        return start_date + datetime.timedelta(days=7)
    else:
        # Default fallback for unknown price types
        return start_date + datetime.timedelta(days=30)


def _generate_task_name(service) -> str:
    """Generate task name based on service and price type."""
    price_type_suffix = ""
    if service.price_type == models.ServicePriceType.MONTHLY:
        price_type_suffix = " - Monthly Service"
    elif service.price_type == models.ServicePriceType.QUARTERLY:
        price_type_suffix = " - Quarterly Service"
    elif service.price_type == models.ServicePriceType.ANNUAL:
        price_type_suffix = " - Annual Service"
    elif service.price_type == models.ServicePriceType.ONE_TIME:
        price_type_suffix = " - One-time Service"

    return f"{service.title}{price_type_suffix}"


def _generate_task_description(service) -> str:
    """Generate task description based on service."""
    description = f"Complete service: {service.title}"
    if service.description:
        description += f"\n\nService Description: {service.description}"

    price_info = f"Service Price: ${service.price:.2f}"
    if service.price_type:
        price_info += f" ({service.price_type})"
    description += f"\n{price_info}"

    return description
