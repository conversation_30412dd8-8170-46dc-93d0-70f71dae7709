import datetime
import os
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Response
from pydantic import BaseModel, field_validator

from records.db import api as db_api, base
from records.db import models
from records.utils import json_utils


class ClientSubscriptionServiceUpdate(BaseModel):
    service_id: str
    is_active: bool = True
    custom_price: Optional[float] = None
    notes: Optional[str] = None


class ClientSubscriptionCreate(BaseModel):
    subscription_id: int
    start_date: Optional[datetime.datetime] = None
    stop_date: Optional[datetime.datetime] = None
    status: str = models.SubscriptionStatus.ACTIVE
    discount_amount: Optional[float] = None
    discount_percent: Optional[float] = None
    notes: Optional[str] = None
    services: Optional[List[ClientSubscriptionServiceUpdate]] = None

    _date_validator = field_validator(
        *['start_date', 'stop_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientSubscriptionUpdate(BaseModel):
    start_date: Optional[datetime.datetime] = None
    stop_date: Optional[datetime.datetime] = None
    status: Optional[str] = None
    discount_amount: Optional[float] = None
    discount_percent: Optional[float] = None
    cancellation_reason: Optional[str] = None
    notes: Optional[str] = None
    services: Optional[List[ClientSubscriptionServiceUpdate]] = None

    _date_validator = field_validator(
        *['start_date', 'stop_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientSubscriptionServiceUpdateSingle(BaseModel):
    is_active: Optional[bool] = None
    custom_price: Optional[float] = None
    notes: Optional[str] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'subscriptions'))

    router.add_api_route("", list_client_subscriptions, methods=['GET'], name='List client subscriptions')
    router.add_api_route("", create_client_subscription, methods=['POST'], name='Create client subscription')
    router.add_api_route("/{client_subscription_id}", get_client_subscription, methods=['GET'], name='Get client subscription')
    router.add_api_route("/{client_subscription_id}", update_client_subscription, methods=['PUT'], name='Update client subscription')
    router.add_api_route("/{client_subscription_id}", delete_client_subscription, methods=['DELETE'], name='Delete client subscription')
    router.add_api_route("/{client_subscription_id}/services/{service_id}", update_client_subscription_service, methods=['PUT'], name='Update client subscription service')

    return router


async def list_client_subscriptions(client_id: str):
    """List all subscriptions for a client."""
    client_subscriptions = await db_api.list_client_subscriptions(client_id=client_id)
    
    # Format response
    formatted_subscriptions = []
    for item in client_subscriptions:
        client_subscription = item['client_subscription']
        subscription = item['subscription']
        services = item['services']
        
        subscription_dict = client_subscription.to_dict()
        subscription_dict['subscription'] = subscription.to_dict()
        subscription_dict['services'] = []
        
        for service_item in services:
            service_dict = service_item['service'].to_dict()
            client_subscription_service = service_item['client_subscription_service']
            service_dict['is_active'] = client_subscription_service.is_active
            service_dict['custom_price'] = client_subscription_service.custom_price
            service_dict['notes'] = client_subscription_service.notes
            subscription_dict['services'].append(service_dict)
        
        formatted_subscriptions.append(subscription_dict)
    
    return {'items': formatted_subscriptions}


async def get_client_subscription(client_id: str, client_subscription_id: int):
    """Get specific client subscription."""
    async with base.session_context():
        client_subscription_db = await db_api.get_client_subscription_by_id(client_subscription_id)
        if not client_subscription_db or client_subscription_db.client_id != client_id:
            raise HTTPException(404, f'Client subscription id={client_subscription_id} not found')

        # Get subscription details
        subscription_db = await db_api.get_subscription_by_id(client_subscription_db.subscription_id)
        
        # Get client subscription services
        client_subscription_services = await db_api.list_client_subscription_services(client_subscription_id)
        
        # Format response
        subscription_dict = client_subscription_db.to_dict()
        subscription_dict['subscription'] = subscription_db.to_dict()
        subscription_dict['services'] = []
        
        for client_subscription_service, service in client_subscription_services:
            service_dict = service.to_dict()
            service_dict['is_active'] = client_subscription_service.is_active
            service_dict['custom_price'] = client_subscription_service.custom_price
            service_dict['notes'] = client_subscription_service.notes
            subscription_dict['services'].append(service_dict)

    return subscription_dict


async def create_client_subscription(client_id: str, client_subscription: ClientSubscriptionCreate):
    """Create a new client subscription."""
    client_subscription_dict = client_subscription.model_dump(exclude_unset=True)
    
    # Validate status
    if not models.SubscriptionStatus.is_valid(client_subscription_dict['status']):
        raise HTTPException(400, f'Invalid status: {client_subscription_dict["status"]}')

    # Set start date to today if not provided
    if not client_subscription_dict.get('start_date'):
        client_subscription_dict['start_date'] = datetime.datetime.now(datetime.timezone.utc)

    # Validate discount
    if client_subscription_dict.get('discount_percent') and client_subscription_dict.get('discount_amount'):
        raise HTTPException(400, 'Cannot specify both discount_percent and discount_amount')

    async with base.session_context():
        # Validate subscription exists
        subscription_db = await db_api.get_subscription_by_id(client_subscription_dict['subscription_id'])
        if not subscription_db:
            raise HTTPException(400, f'Subscription id={client_subscription_dict["subscription_id"]} does not exist')

        # Validate client exists
        client_db = await db_api.get_client_by_id(client_id)
        if not client_db:
            raise HTTPException(400, f'Client id={client_id} does not exist')

        # Create client subscription
        services_config = client_subscription_dict.pop('services', None)
        client_subscription_dict['client_id'] = client_id
        
        # Calculate initial total price (will be recalculated after services are added)
        client_subscription_dict['total_price'] = 0.0
        
        db_client_subscription = await db_api.create_client_subscription(client_subscription_dict)

        # Get default services from subscription
        subscription_services = await db_api.list_subscription_services(subscription_db.id)
        
        # Create client subscription services
        for subscription_service, service in subscription_services:
            # Check if this service has custom configuration
            service_config = None
            if services_config:
                service_config = next((s for s in services_config if s['service_id'] == service.id), None)
            
            client_subscription_service_dict = {
                'client_subscription_id': db_client_subscription.id,
                'service_id': service.id,
                'is_active': service_config['is_active'] if service_config else True,
                'custom_price': service_config.get('custom_price') if service_config else None,
                'notes': service_config.get('notes') if service_config else None
            }
            
            await db_api.create_client_subscription_service(client_subscription_service_dict)

        # Calculate and update total price
        total_price = await db_api.calculate_subscription_total_price(db_client_subscription.id)
        
        # Apply discounts
        if client_subscription_dict.get('discount_percent'):
            total_price = total_price * (1 - client_subscription_dict['discount_percent'] / 100)
        elif client_subscription_dict.get('discount_amount'):
            total_price = max(0, total_price - client_subscription_dict['discount_amount'])
        
        await db_api.update_client_subscription(db_client_subscription, {'total_price': total_price})

    return await get_client_subscription(client_id, db_client_subscription.id)


async def update_client_subscription(client_id: str, client_subscription_id: int, client_subscription: ClientSubscriptionUpdate):
    """Update client subscription."""
    client_subscription_dict = client_subscription.model_dump(exclude_unset=True)
    if not client_subscription_dict:
        raise HTTPException(400, 'No fields to update')

    # Validate status if provided
    if 'status' in client_subscription_dict and not models.SubscriptionStatus.is_valid(client_subscription_dict['status']):
        raise HTTPException(400, f'Invalid status: {client_subscription_dict["status"]}')

    # Validate discount
    if client_subscription_dict.get('discount_percent') and client_subscription_dict.get('discount_amount'):
        raise HTTPException(400, 'Cannot specify both discount_percent and discount_amount')

    async with base.session_context():
        client_subscription_db = await db_api.get_client_subscription_by_id(client_subscription_id)
        if not client_subscription_db or client_subscription_db.client_id != client_id:
            raise HTTPException(404, f'Client subscription id={client_subscription_id} not found')

        # Handle cancellation
        if client_subscription_dict.get('status') == models.SubscriptionStatus.CANCELLED:
            client_subscription_dict['cancellation_at'] = datetime.datetime.now(datetime.timezone.utc)

        # Update services if provided
        services_config = client_subscription_dict.pop('services', None)
        if services_config is not None:
            for service_config in services_config:
                # Find existing client subscription service
                query_result = await db_api.list_client_subscription_services(client_subscription_id)
                existing_service = next(
                    (css for css, s in query_result if s.id == service_config['service_id']),
                    None
                )

                if existing_service:
                    service_update_dict = {
                        'is_active': service_config.get('is_active', existing_service.is_active),
                        'custom_price': service_config.get('custom_price'),
                        'notes': service_config.get('notes')
                    }
                    await db_api.update_client_subscription_service(existing_service, service_update_dict)

        # Update client subscription
        if client_subscription_dict:
            updated_client_subscription = await db_api.update_client_subscription(client_subscription_db, client_subscription_dict)
        else:
            updated_client_subscription = client_subscription_db

        # Recalculate total price if services were updated or discount changed
        if services_config is not None or 'discount_percent' in client_subscription_dict or 'discount_amount' in client_subscription_dict:
            total_price = await db_api.calculate_subscription_total_price(client_subscription_id)

            # Apply discounts
            if updated_client_subscription.discount_percent:
                total_price = total_price * (1 - updated_client_subscription.discount_percent / 100)
            elif updated_client_subscription.discount_amount:
                total_price = max(0, total_price - updated_client_subscription.discount_amount)

            await db_api.update_client_subscription(updated_client_subscription, {'total_price': total_price})

    return await get_client_subscription(client_id, client_subscription_id)


async def delete_client_subscription(client_id: str, client_subscription_id: int):
    """Delete client subscription."""
    async with base.session_context():
        client_subscription_db = await db_api.get_client_subscription_by_id(client_subscription_id)
        if not client_subscription_db or client_subscription_db.client_id != client_id:
            raise HTTPException(404, f'Client subscription id={client_subscription_id} not found')

        await db_api.delete_client_subscription(client_subscription_id)

    return Response(status_code=204)


async def update_client_subscription_service(
    client_id: str,
    client_subscription_id: int,
    service_id: str,
    service_update: ClientSubscriptionServiceUpdateSingle
):
    """Update a specific service within a client subscription."""
    service_update_dict = service_update.model_dump(exclude_unset=True)
    if not service_update_dict:
        raise HTTPException(400, 'No fields to update')

    async with base.session_context():
        # Validate client subscription exists and belongs to client
        client_subscription_db = await db_api.get_client_subscription_by_id(client_subscription_id)
        if not client_subscription_db or client_subscription_db.client_id != client_id:
            raise HTTPException(404, f'Client subscription id={client_subscription_id} not found')

        # Find the specific client subscription service
        query_result = await db_api.list_client_subscription_services(client_subscription_id)
        client_subscription_service = next(
            (css for css, s in query_result if s.id == service_id),
            None
        )

        if not client_subscription_service:
            raise HTTPException(404, f'Service id={service_id} not found in subscription')

        # Update the service
        await db_api.update_client_subscription_service(client_subscription_service, service_update_dict)

        # Recalculate total price
        total_price = await db_api.calculate_subscription_total_price(client_subscription_id)

        # Apply discounts
        if client_subscription_db.discount_percent:
            total_price = total_price * (1 - client_subscription_db.discount_percent / 100)
        elif client_subscription_db.discount_amount:
            total_price = max(0, total_price - client_subscription_db.discount_amount)

        await db_api.update_client_subscription(client_subscription_db, {'total_price': total_price})

    return await get_client_subscription(client_id, client_subscription_id)
