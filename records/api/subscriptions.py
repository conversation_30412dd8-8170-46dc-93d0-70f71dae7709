import os
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, Response
from pydantic import BaseModel

from records.api import auth_utils
from records.db import api as db_api, base
from records.db import models


class SubscriptionServiceCreate(BaseModel):
    service_id: str


class SubscriptionCreate(BaseModel):
    title: str
    price: float
    price_type: str  # Monthly, Quarterly, Annual, One-time
    description: Optional[str] = None
    services: Optional[List[SubscriptionServiceCreate]] = None


class SubscriptionUpdate(BaseModel):
    title: Optional[str] = None
    price: Optional[float] = None
    price_type: Optional[str] = None
    description: Optional[str] = None
    services: Optional[List[SubscriptionServiceCreate]] = None


def get_router(prefix='/'):
    router = APIRouter(
        prefix=os.path.join(prefix, 'subscriptions'),
        dependencies=[Depends(auth_utils.owner_access)]
    )

    router.add_api_route("", list_subscriptions, methods=['GET'], name='List subscriptions')
    router.add_api_route("", create_subscription, methods=['POST'], name='Create subscription')
    router.add_api_route("/{subscription_id}", get_subscription, methods=['GET'], name='Get subscription')
    router.add_api_route("/{subscription_id}", update_subscription, methods=['PUT'], name='Update subscription')
    router.add_api_route("/{subscription_id}", delete_subscription, methods=['DELETE'], name='Delete subscription')

    return router


async def list_subscriptions(q: str = None):
    """List all subscriptions with their services."""
    subscriptions = await db_api.list_subscriptions(q=q)
    subscription_dicts = [s.to_dict() for s in subscriptions]

    return {'items': subscription_dicts, 'count': len(subscription_dicts)}


async def get_subscription(subscription_id: int):
    """Get subscription by ID with its services."""
    async with base.session_context():
        db_subscription = await db_api.get_subscription_by_id(subscription_id)
        if not db_subscription:
            raise HTTPException(404, f'Subscription id={subscription_id} not found')

        # Get subscription services
        subscription_services = await db_api.list_subscription_services(subscription_id=subscription_id)
        db_subscription.services = [service for _, service in subscription_services]

    return db_subscription.to_dict()


async def create_subscription(subscription: SubscriptionCreate):
    """Create a new subscription with services."""
    subscription_dict = subscription.model_dump()
    
    # Validate price type
    if not models.ServicePriceType.is_valid(subscription_dict['price_type']):
        raise HTTPException(400, f'Invalid price_type: {subscription_dict["price_type"]}')

    async with base.session_context():
        # Check if subscription with this title already exists
        existing_subscription = await db_api.get_subscription_by_title(subscription_dict['title'])
        if existing_subscription:
            raise HTTPException(400, f'Subscription with title "{subscription_dict["title"]}" already exists')

        # Create subscription
        services = subscription_dict.pop('services', [])
        db_subscription = await db_api.create_subscription(subscription_dict)
        
        # Create subscription services
        services_db = []
        for service_data in services:
            # Validate service exists
            service_db = await db_api.get_service_by_id(service_data['service_id'])
            if not service_db:
                raise HTTPException(400, f'Service id={service_data["service_id"]} does not exist')
            
            subscription_service_dict = {
                'subscription_id': db_subscription.id,
                'service_id': service_data['service_id']
            }
            await db_api.create_subscription_service(subscription_service_dict)
            services_db.append(service_db)

    subscription_dict = db_subscription.to_dict()
    subscription_dict['services'] = [s.to_dict() for s in services_db]
    return subscription_dict


async def update_subscription(subscription_id: int, subscription: SubscriptionUpdate):
    """Update subscription and its services."""
    subscription_dict = subscription.model_dump(exclude_unset=True)
    if not subscription_dict:
        raise HTTPException(400, 'No fields to update')

    # Validate price type if provided
    if 'price_type' in subscription_dict and not models.ServicePriceType.is_valid(subscription_dict['price_type']):
        raise HTTPException(400, f'Invalid price_type: {subscription_dict["price_type"]}')

    async with base.session_context():
        db_subscription = await db_api.get_subscription_by_id(subscription_id)
        if not db_subscription:
            raise HTTPException(404, f'Subscription id={subscription_id} not found')

        # Update subscription
        services = subscription_dict.pop('services', None)
        if subscription_dict:
            updated_subscription = await db_api.update_subscription(db_subscription, subscription_dict)
        else:
            updated_subscription = db_subscription

        # Update services if provided
        services_db = []
        if services is not None:
            # Delete all existing subscription services
            await db_api.delete_subscription_services(subscription_id=subscription_id)
            
            # Create new subscription services
            for service_data in services:
                # Validate service exists
                service_db = await db_api.get_service_by_id(service_data['service_id'])
                if not service_db:
                    raise HTTPException(400, f'Service id={service_data["service_id"]} does not exist')
                
                subscription_service_dict = {
                    'subscription_id': subscription_id,
                    'service_id': service_data['service_id']
                }
                await db_api.create_subscription_service(subscription_service_dict)
                services_db.append(service_db)
        else:
            # Get existing services
            subscription_services = await db_api.list_subscription_services(subscription_id=subscription_id)
            services_db = [service for _, service in subscription_services]

    subscription_dict = updated_subscription.to_dict()
    subscription_dict['services'] = [s.to_dict() for s in services_db]
    return subscription_dict


async def delete_subscription(subscription_id: int):
    """Delete subscription and its services."""
    async with base.session_context():
        db_subscription = await db_api.get_subscription_by_id(subscription_id)
        if not db_subscription:
            raise HTTPException(404, f'Subscription id={subscription_id} not found')

        await db_api.delete_subscription(subscription_id)

    return Response(status_code=204)
