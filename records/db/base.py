import contextlib
import datetime
import logging
import os
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy import create_engine
from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import sessionmaker as sessionmaker_sync

from records.context import context
from records.exceptions import NotFoundException
from records.utils import utils, blowfish_utils

logger = logging.getLogger(__name__)
_ENGINE = None
_ENGINE_SYNC = None
_SESSION_NAME = 'session'


class _ModelBase(object):
    """Base class for all Mistral SQLAlchemy DB Models."""

    __table__ = None
    __encrypt_columns__ = None
    __to_dict__ = []

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def update(self, values):
        """Make the model object behave like a dict."""
        for k, v in values.items():
            setattr(self, k, v)

    def __setattr__(self, key, value):
        if self.__encrypt_columns__ and key in self.__encrypt_columns__:
            if isinstance(value, dict):
                value = blowfish_utils.encrypt_dict(value)
            else:
                if value is None:
                    super().__setattr__(key, value)
                    return
                value = blowfish_utils.encrypt_string(value)
        super().__setattr__(key, value)

    def to_dict(self, decrypt=True):
        """sqlalchemy based automatic to_dict method."""
        d = {}

        for col in self.__table__.columns.keys() + self.__to_dict__:
            if not hasattr(self, col):
                continue
            v = getattr(self, col)
            if isinstance(v, datetime.datetime):
                d[col] = datetime_to_string(v)
            elif isinstance(v, datetime.date):
                d[col] = date_to_string(v)
            elif isinstance(v, ModelBase):
                d[col] = v.to_dict()
            elif isinstance(v, list):
                d[col] = [i.to_dict() for i in v]
            else:
                d[col] = v

            if self.__encrypt_columns__ and col in self.__encrypt_columns__ and decrypt:
                if v is None:
                    continue
                if isinstance(v, dict):
                    d[col] = blowfish_utils.decrypt_dict(d[col])
                else:
                    d[col] = blowfish_utils.decrypt_string(d[col])

        return d

    def __repr__(self):
        return '%s %s' % (type(self).__name__, self.to_dict(decrypt=False).__repr__())


def datetime_to_string(date: datetime.datetime):
    if not date:
        return None
    utc_date = date.astimezone(datetime.timezone.utc)
    return datetime.datetime.strftime(utc_date, '%Y-%m-%d %H:%M:%S')


def date_to_string(date: datetime.date):
    if not date:
        return None
    utc_date = date.astimezone(datetime.timezone.utc)
    return utc_date.isoformat()


ModelBase = declarative_base(cls=_ModelBase)


# ModelBase = declarative.declarative_base(cls=_ModelBase)


def get_engine():
    global _ENGINE

    if not _ENGINE:
        debug_db = os.environ.get('DEBUG_DB', 'false').lower() == 'true'
        logging.getLogger('sqlalchemy.engine').propagate = False
        _ENGINE = create_async_engine(
            utils.generate_connection_string(),
            echo=debug_db,
            pool_pre_ping=True,
            pool_size=20,
            max_overflow=20,
            pool_recycle=600,
        )

    return _ENGINE


def get_sync_engine():
    global _ENGINE_SYNC

    if not _ENGINE_SYNC:
        debug_db = os.environ.get('DEBUG_DB', 'false').lower() == 'true'
        logging.getLogger('sqlalchemy.engine').propagate = False
        _ENGINE_SYNC = create_engine(
            utils.generate_connection_string('psycopg2'),
            echo=debug_db,
            pool_pre_ping=True,
            pool_recycle=60,
        )

    return _ENGINE_SYNC


def get_session_sync():
    session_maker = sessionmaker_sync(
        bind=get_sync_engine(),
        expire_on_commit=False,
    )

    # return orm.scoped_session(session_maker)
    return session_maker()


def get_session():
    session_maker = sessionmaker(
        bind=get_engine(),
        expire_on_commit=False,
        class_=AsyncSession,
    )

    # return orm.scoped_session(session_maker)
    return session_maker()


def _get_thread_local_session():
    return context.db.value


def _get_or_create_thread_local_session():
    ses = _get_thread_local_session()

    if ses:
        return ses, False

    ses = get_session()

    _set_thread_local_session(ses)

    return ses, True


def _set_thread_local_session(session):
    context.db.set(session)


def session_aware(param_name: Optional[str] = "session"):
    """Decorator for methods working within db session."""

    def _decorator(func):
        async def _within_session(*args, **kw):
            # If 'created' flag is True it means that the transaction is
            # demarcated explicitly outside this module.
            ses, created = _get_or_create_thread_local_session()

            try:
                kw[param_name] = ses

                result = await func(*args, **kw)

                if created:
                    await ses.commit()

                return result
            except Exception as e:
                if created:
                    await ses.rollback()
                if 'duplicate key value violates unique constraint' in str(e):
                    logger.error(f"Duplicate entry for inserting item: {e}")
                    raise HTTPException(409, "Duplicate entry for inserting item")
                raise
            finally:
                if created:
                    _set_thread_local_session(None)
                    await ses.close()

        _within_session.__doc__ = func.__doc__

        return _within_session

    return _decorator


@contextlib.asynccontextmanager
async def session_context():
    """Context manager for methods working within db session."""
    # If 'created' flag is True it means that the transaction is
    # demarcated explicitly outside this module.
    ses, created = _get_or_create_thread_local_session()

    try:
        yield ses
        if created:
            await ses.commit()
    except Exception as e:
        if created:
            await ses.rollback()
        if 'duplicate key value violates unique constraint' in str(e):
            logger.error(f"Duplicate entry for inserting item: {e}")
            raise HTTPException(409, "Duplicate entry for inserting item")
        raise
    finally:
        if created:
            _set_thread_local_session(None)
            await ses.close()


def session_aware_sync(param_name="session"):
    """Decorator for methods working within db session."""

    def _decorator(func):
        def _within_session(*args, **kw):
            # If 'created' flag is True it means that the transaction is
            # demarcated explicitly outside this module.
            ses = context.db.value

            if ses:
                created = False
            else:
                ses = get_session_sync()
                context.db.set(ses)
                created = True

            try:
                kw[param_name] = ses

                result = func(*args, **kw)

                if created:
                    ses.commit()

                return result
            except Exception:
                if created:
                    ses.rollback()
                raise
            finally:
                if created:
                    context.db.set(None)
                    ses.close()

        _within_session.__doc__ = func.__doc__

        return _within_session

    return _decorator


def model_query(model):
    """Query helper.

    :param model: base model to query
    """
    return select(model)


@session_aware()
async def create_model(model, values, session=None):
    if isinstance(values, model):
        instance = values
    else:
        instance = model(**values)

    try:
        session.add(instance)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for {model.__name__}: %s" % e
        )

    return instance


@session_aware()
async def get_by_id(model, id: int | str, notfoundok=False, session=None):
    q = select(model).where(model.id == id)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'{model.__name__} not found for id: {id}')
    return res


@session_aware()
async def list_by_x(model, session=None, **kwargs):
    q = select(model)
    if not kwargs:
        raise ValueError('No filter arguments provided')
    for key, value in kwargs.items():
        if isinstance(value, list):
            q = q.where(getattr(model, key).in_(value))
        else:
            q = q.where(getattr(model, key) == value)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalars().fetchall()

    return res


@session_aware()
async def delete_by_x(model, session=None, **kwargs):
    q = delete(model)
    if not kwargs:
        raise ValueError('No filter arguments provided')
    for key, value in kwargs.items():
        q = q.where(getattr(model, key) == value)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)

    return res


@session_aware()
async def delete_client_datas(model, ids: list[int] = None, client_id: str = None, session=None):
    if not ids and not client_id:
        raise ValueError('Either ids or client_id must be provided')
    delete_q = delete(model)
    if ids:
        delete_q = delete_q.where(model.id.in_(ids))
    if client_id:
        delete_q = delete_q.where(model.client_id == client_id)
    await session.execute(delete_q)


@session_aware()
async def get_by_x(model, notfoundok=False, session=None, **kwargs):
    q = select(model)
    if not kwargs:
        raise ValueError('No filter arguments provided')
    for key, value in kwargs.items():
        q = q.where(getattr(model, key) == value)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    if not res and not notfoundok:
        raise NotFoundException(f'{model.__name__} not found for args: {kwargs}')
    return res


@session_aware()
async def delete_by_id(model, id: int | str, session=None):
    delete_q = delete(model).where(model.id == id)
    res = await session.execute(delete_q)
    return res.rowcount


@session_aware()
async def update_model(model_instance, values, session=None):
    """Update a model instance with new values."""
    values['updated_at'] = now()
    update_q = update(type(model_instance)).where(type(model_instance).id == model_instance.id)
    update_q = update_q.values(values)

    await session.execute(update_q)
    model_instance.update(values)

    return model_instance


@session_aware()
async def mark_as_delete(model, id: int | str, session=None):
    values = {'deleted_at': now()}
    update_q = update(model).where(model.id == id)
    update_q = update_q.values(values)

    return await session.execute(update_q)


def now():
    return datetime.datetime.now(datetime.timezone.utc)
