from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_subscription(values, session=None):
    """Create a new client subscription."""
    client_subscription = models.ClientSubscription(**values)

    try:
        session.add(client_subscription)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for ClientSubscription: {e}"
        )

    return client_subscription


@base.session_aware()
async def create_client_subscription_service(values, session=None):
    """Create a client subscription service relationship."""
    client_subscription_service = models.ClientSubscriptionService(**values)

    try:
        session.add(client_subscription_service)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for ClientSubscriptionService: {e}"
        )

    return client_subscription_service


@base.session_aware()
async def get_client_subscription_by_id(client_subscription_id: int, session=None):
    """Get client subscription by ID."""
    return await base.get_by_id(models.ClientSubscription, client_subscription_id, session=session)


@base.session_aware()
async def get_client_subscription_service_by_id(client_subscription_service_id: int, session=None):
    """Get client subscription service by ID."""
    return await base.get_by_id(models.ClientSubscriptionService, client_subscription_service_id, session=session)


@base.session_aware()
async def list_client_subscriptions(client_id: str, session=None):
    """List all subscriptions for a client with their services."""
    query = select(
        models.ClientSubscription,
        models.Subscription,
        models.ClientSubscriptionService,
        models.Service
    )
    query = query.where(models.ClientSubscription.client_id == client_id)
    query = query.join(models.Subscription, models.ClientSubscription.subscription_id == models.Subscription.id)
    query = query.outerjoin(models.ClientSubscriptionService, models.ClientSubscription.id == models.ClientSubscriptionService.client_subscription_id)
    query = query.outerjoin(models.Service, models.ClientSubscriptionService.service_id == models.Service.id)
    query = query.order_by(models.ClientSubscription.id, models.Service.title)

    joined_res = await session.execute(query)
    joined_res = joined_res.fetchall()

    client_subscriptions = {}
    for client_subscription, subscription, client_subscription_service, service in joined_res:
        if client_subscription.id not in client_subscriptions:
            client_subscriptions[client_subscription.id] = {
                'client_subscription': client_subscription,
                'subscription': subscription,
                'services': []
            }
        if service and client_subscription_service:
            client_subscriptions[client_subscription.id]['services'].append({
                'client_subscription_service': client_subscription_service,
                'service': service
            })

    return list(client_subscriptions.values())


@base.session_aware()
async def list_client_subscription_services(client_subscription_id: int, session=None):
    """List services for a specific client subscription."""
    query = select(models.ClientSubscriptionService, models.Service)
    query = query.join(models.Service, models.ClientSubscriptionService.service_id == models.Service.id)
    query = query.where(models.ClientSubscriptionService.client_subscription_id == client_subscription_id)
    query = query.order_by(models.Service.title)
    
    res = await session.execute(query)
    return [(client_subscription_service, service) for client_subscription_service, service in res.fetchall()]


@base.session_aware()
async def update_client_subscription(client_subscription, values, session=None):
    """Update client subscription."""
    return await base.update_model(client_subscription, values, session=session)


@base.session_aware()
async def update_client_subscription_service(client_subscription_service, values, session=None):
    """Update client subscription service."""
    return await base.update_model(client_subscription_service, values, session=session)


@base.session_aware()
async def delete_client_subscription(client_subscription_id: int, session=None):
    """Delete client subscription and its services."""
    # First delete client subscription services
    await delete_client_subscription_services(client_subscription_id=client_subscription_id, session=session)
    
    # Then delete client subscription
    delete_q = delete(models.ClientSubscription).where(models.ClientSubscription.id == client_subscription_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_subscription_service(client_subscription_service_id: int, session=None):
    """Delete a specific client subscription service."""
    delete_q = delete(models.ClientSubscriptionService).where(models.ClientSubscriptionService.id == client_subscription_service_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_subscription_services(client_subscription_id: int = None, ids: list[int] = None, session=None):
    """Delete client subscription services."""
    if not client_subscription_id and not ids:
        raise ValueError('Either client_subscription_id or ids must be provided')
    
    delete_q = delete(models.ClientSubscriptionService)
    if client_subscription_id:
        delete_q = delete_q.where(models.ClientSubscriptionService.client_subscription_id == client_subscription_id)
    if ids:
        delete_q = delete_q.where(models.ClientSubscriptionService.id.in_(ids))
    
    await session.execute(delete_q)


@base.session_aware()
async def calculate_subscription_total_price(client_subscription_id: int, session=None):
    """Calculate total price for a client subscription based on active services."""
    query = select(models.ClientSubscriptionService, models.Service)
    query = query.join(models.Service, models.ClientSubscriptionService.service_id == models.Service.id)
    query = query.where(
        models.ClientSubscriptionService.client_subscription_id == client_subscription_id,
        models.ClientSubscriptionService.is_active == True
    )
    
    res = await session.execute(query)
    services = res.fetchall()
    
    total_price = 0.0
    for client_subscription_service, service in services:
        # Use custom price if set, otherwise use service price
        price = client_subscription_service.custom_price if client_subscription_service.custom_price is not None else service.price
        total_price += price
    
    return total_price
