
from sqlalchemy import delete, update
from sqlalchemy.future import select

from records.db import base
from records.db import models


# ServiceComponent and ClientServiceComponent models have been removed
# in favor of the new subscription-based architecture.
# This file is kept for backward compatibility but functions are deprecated.


@base.session_aware()
async def delete_client_service_components(ids: list[int] = None, client_service_id: int = None, session=None):
    return await base.delete_by_x(models.ClientServiceComponent, id=ids, client_service_id=client_service_id, session=session)


@base.session_aware()
async def list_client_service_components(
    client_service_id: int,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.ClientServiceComponent).where(models.ClientServiceComponent.client_service_id == client_service_id)
    if not hasattr(models.ClientServiceComponent, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientServiceComponent, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall()
