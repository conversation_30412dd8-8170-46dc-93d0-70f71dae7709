from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_subscription(values, session=None):
    """Create a new subscription."""
    subscription = models.Subscription(**values)

    try:
        session.add(subscription)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for Subscription: {e}"
        )

    return subscription


@base.session_aware()
async def create_subscription_service(values, session=None):
    """Create a subscription-service relationship."""
    subscription_service = models.SubscriptionService(**values)

    try:
        session.add(subscription_service)
        await session.flush()
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for SubscriptionService: {e}"
        )

    return subscription_service


@base.session_aware()
async def get_subscription_by_id(subscription_id: int, session=None):
    """Get subscription by ID."""
    return await base.get_by_id(models.Subscription, subscription_id, session=session)


@base.session_aware()
async def get_subscription_by_title(title: str, session=None):
    """Get subscription by title."""
    query = select(models.Subscription).where(models.Subscription.title == title)
    res = await session.execute(query)
    return res.scalar()


@base.session_aware()
async def list_subscriptions(q: str = None, session=None):
    """List all subscriptions with their services."""
    query = select(models.Subscription, models.SubscriptionService, models.Service)
    query = query.outerjoin(models.SubscriptionService, models.Subscription.id == models.SubscriptionService.subscription_id)
    query = query.outerjoin(models.Service, models.SubscriptionService.service_id == models.Service.id)
    query = query.order_by(models.Subscription.title, models.Service.title)
    
    if q:
        query = query.where(models.Subscription.title.ilike(f'%{q}%'))
    
    joined_res = await session.execute(query)
    joined_res = joined_res.fetchall()

    subscriptions = {}
    for subscription, subscription_service, service in joined_res:
        if subscription.id not in subscriptions:
            subscriptions[subscription.id] = subscription
            subscriptions[subscription.id].services = []
        if service:
            subscriptions[subscription.id].services.append(service)

    return list(subscriptions.values())


@base.session_aware()
async def list_subscription_services(subscription_id: int, session=None):
    """List services for a specific subscription."""
    query = select(models.SubscriptionService, models.Service)
    query = query.join(models.Service, models.SubscriptionService.service_id == models.Service.id)
    query = query.where(models.SubscriptionService.subscription_id == subscription_id)
    query = query.order_by(models.Service.title)
    
    res = await session.execute(query)
    return [(subscription_service, service) for subscription_service, service in res.fetchall()]


@base.session_aware()
async def update_subscription(subscription, values, session=None):
    """Update subscription."""
    return await base.update_model(subscription, values, session=session)


@base.session_aware()
async def delete_subscription(subscription_id: int, session=None):
    """Delete subscription and its services."""
    # First delete subscription services
    await delete_subscription_services(subscription_id=subscription_id, session=session)
    
    # Then delete subscription
    delete_q = delete(models.Subscription).where(models.Subscription.id == subscription_id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_subscription_services(subscription_id: int = None, ids: list[int] = None, session=None):
    """Delete subscription services."""
    if not subscription_id and not ids:
        raise ValueError('Either subscription_id or ids must be provided')
    
    delete_q = delete(models.SubscriptionService)
    if subscription_id:
        delete_q = delete_q.where(models.SubscriptionService.subscription_id == subscription_id)
    if ids:
        delete_q = delete_q.where(models.SubscriptionService.id.in_(ids))
    
    await session.execute(delete_q)
