#!/usr/bin/env python3
"""
Test script for the new subscriptions API.
This script tests both global subscriptions and client subscriptions.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_imports():
    """Test that all imports work correctly."""
    try:
        print("Testing imports...")
        
        # Test DB models
        from records.db import models
        print("✓ DB models imported successfully")
        
        # Test DB API functions
        from records.db import api as db_api
        print("✓ DB API imported successfully")
        
        # Test subscription APIs
        from records.api import subscriptions
        from records.api import client_subscriptions
        print("✓ API modules imported successfully")
        
        # Test that new models exist
        assert hasattr(models, 'Subscription')
        assert hasattr(models, 'SubscriptionService')
        assert hasattr(models, 'ClientSubscription')
        assert hasattr(models, 'ClientSubscriptionService')
        assert hasattr(models, 'SubscriptionStatus')
        print("✓ All new models are available")

        # Test that Service model still exists (it should)
        assert hasattr(models, 'Service')
        assert hasattr(models, 'ClientService')
        print("✓ Service models are available")
        
        # Test that new DB functions exist
        assert hasattr(db_api, 'create_subscription')
        assert hasattr(db_api, 'list_subscriptions')
        assert hasattr(db_api, 'create_client_subscription')
        assert hasattr(db_api, 'list_client_subscriptions')
        print("✓ All new DB functions are available")
        
        print("\n🎉 All imports successful! The subscription system is ready to use.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except AttributeError as e:
        print(f"❌ Missing attribute: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def main():
    """Main test function."""
    print("=== Subscription API Test ===\n")
    
    success = await test_imports()
    
    if success:
        print("\n=== Next Steps ===")
        print("1. Run database migrations to create the new tables")
        print("2. Start the FastAPI server")
        print("3. Test the API endpoints:")
        print("   - GET /api/v1/subscriptions - List global subscriptions")
        print("   - POST /api/v1/subscriptions - Create subscription")
        print("   - GET /api/v1/clients/{client_id}/subscriptions - List client subscriptions")
        print("   - POST /api/v1/clients/{client_id}/subscriptions - Create client subscription")
        print("\n=== API Documentation ===")
        print("Visit http://localhost:8000/docs to see the interactive API documentation")
    else:
        print("\n❌ Tests failed. Please fix the issues above before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
